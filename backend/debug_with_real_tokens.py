#!/usr/bin/env python3
"""
Updated Debug <PERSON>ript - Uses Real Database Tokens

This script uses real FCM tokens from the database instead of hardcoded ones.
"""

import json
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass

import firebase_admin
from firebase_admin import messaging
from database._client import db

def get_real_token_from_database():
    """Get a real FCM token from database"""
    try:
        users_ref = db.collection('users')
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                return user_data['fcm_token'], doc.id
        return None, None
    except Exception as e:
        print(f"Error getting token: {e}")
        return None, None

def test_with_real_token():
    """Test notification with real database token"""
    # Initialize Firebase
    if not os.environ.get('SERVICE_ACCOUNT_JSON'):
        print("❌ SERVICE_ACCOUNT_JSON not found")
        return
    
    try:
        firebase_admin.get_app()
    except ValueError:
        service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        credentials = firebase_admin.credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(credentials)
    
    # Get real token
    token, uid = get_real_token_from_database()
    if not token:
        print("❌ No FCM token found in database")
        return
    
    print(f"🧪 Testing with real token from user: {uid[:8]}...")
    print(f"   Token: {token[:30]}...")
    
    # Test notification
    try:
        message = messaging.Message(
            notification=messaging.Notification(
                title="Real Token Test",
                body="Testing with real database token"
            ),
            token=token
        )
        
        response = messaging.send(message)
        print(f"✅ Success: {response}")
        
    except Exception as e:
        print(f"❌ Failed: {e}")

if __name__ == "__main__":
    test_with_real_token()
