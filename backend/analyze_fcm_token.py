#!/usr/bin/env python3
"""
FCM Token Analyzer

This script analyzes FCM tokens to determine which Bundle ID/App ID they belong to.

Usage:
    python3 analyze_fcm_token.py
"""

import json
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("⚠️ python-dotenv not available")

from database._client import db

def analyze_hardcoded_token():
    """Analyze the hardcoded token in debug script"""
    print("🔍 ANALYZING HARDCODED TOKEN IN DEBUG SCRIPT")
    print("=" * 60)
    
    hardcoded_token = "cWixBO7OeEdakAgoUImAXE:APA91bHSeTeQegPJzHxceH5Qpv8GlJlyZ1GCTRW8xYfHHfhLbrDnZ4hBtuxC5hQHU8jV0CVQLKiiV1C8l_DWeqo2IPfZtK199cgAMrSBzw18QJX38hkK000"
    
    print(f"硬编码的 FCM Token:")
    print(f"  {hardcoded_token}")
    print(f"  长度: {len(hardcoded_token)} 字符")
    print(f"  前缀: {hardcoded_token[:20]}...")
    print()
    
    # Check if this token exists in database
    print("🔍 检查此 token 是否在数据库中...")
    
    try:
        users_ref = db.collection('users')
        found_user = None
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token'] == hardcoded_token:
                found_user = {
                    'uid': doc.id,
                    'email': user_data.get('email', 'Unknown'),
                    'time_zone': user_data.get('time_zone', 'Unknown'),
                    'created_at': user_data.get('created_at', 'Unknown')
                }
                break
        
        if found_user:
            print(f"✅ 在数据库中找到此 token:")
            print(f"  用户 UID: {found_user['uid']}")
            print(f"  邮箱: {found_user['email']}")
            print(f"  时区: {found_user['time_zone']}")
            print(f"  创建时间: {found_user['created_at']}")
        else:
            print(f"❌ 此 token 不在当前数据库中")
            print(f"   可能是:")
            print(f"   • 旧的/过期的 token")
            print(f"   • 来自不同项目的 token")
            print(f"   • 测试用的假 token")
    
    except Exception as e:
        print(f"❌ 检查数据库时出错: {e}")

def get_all_tokens_from_database():
    """Get all FCM tokens from database with user info"""
    print("\n🔍 数据库中的所有 FCM TOKENS")
    print("=" * 60)
    
    try:
        users_ref = db.collection('users')
        tokens_info = []
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                tokens_info.append({
                    'uid': doc.id,
                    'token': user_data['fcm_token'],
                    'email': user_data.get('email', 'Unknown'),
                    'time_zone': user_data.get('time_zone', 'Unknown')
                })
        
        print(f"找到 {len(tokens_info)} 个用户的 FCM tokens:")
        print()
        
        for i, token_info in enumerate(tokens_info, 1):
            print(f"{i}. 用户 UID: {token_info['uid'][:12]}...")
            print(f"   邮箱: {token_info['email']}")
            print(f"   Token: {token_info['token'][:30]}...")
            print(f"   完整 Token: {token_info['token']}")
            print()
        
        return tokens_info
        
    except Exception as e:
        print(f"❌ 获取数据库 tokens 时出错: {e}")
        return []

def analyze_token_bundle_id_relationship():
    """Analyze relationship between tokens and Bundle IDs"""
    print("\n🔍 TOKEN 与 BUNDLE ID 的关系分析")
    print("=" * 60)
    
    print("FCM Token 的生成规则:")
    print("• FCM tokens 由 iOS 应用生成")
    print("• 每个 token 关联到特定的:")
    print("  - Firebase 项目 (chat10000-402ac)")
    print("  - Bundle ID (me.memorion 或 me.memorion.dev)")
    print("  - 设备和应用实例")
    print()
    
    print("当前情况分析:")
    print("• debug_service_account_detailed.py 使用硬编码 token")
    print("• 这个 token 可能是用旧 Bundle ID 生成的")
    print("• 如果 APNs 配置只针对新 Bundle ID，旧 token 会失败")
    print()
    
    print("解决方案:")
    print("1. 使用数据库中的真实 token 进行测试")
    print("2. 确保 iOS 应用用新 Bundle ID 重新生成 token")
    print("3. 为新 Bundle ID 配置 APNs 认证")

def provide_token_update_recommendations():
    """Provide recommendations for updating tokens"""
    print("\n💡 TOKEN 更新建议")
    print("=" * 60)
    
    print("修改 debug_service_account_detailed.py:")
    print("1. 不要使用硬编码 token")
    print("2. 从数据库动态获取 token")
    print("3. 使用最新生成的 token 进行测试")
    print()
    
    print("iOS 应用端:")
    print("1. 确保应用使用新 Bundle ID 构建")
    print("2. 重新安装应用以生成新 token")
    print("3. 验证新 token 被正确发送到后端")
    print()
    
    print("Firebase 配置:")
    print("1. 为 me.memorion 配置 APNs 认证")
    print("2. 为 me.memorion.dev 配置 APNs 认证")
    print("3. 确保 Bundle ID 完全匹配")

def create_updated_debug_script():
    """Create an updated debug script that uses database tokens"""
    print("\n📝 创建更新的调试脚本")
    print("=" * 60)
    
    updated_script = '''#!/usr/bin/env python3
"""
Updated Debug Script - Uses Real Database Tokens

This script uses real FCM tokens from the database instead of hardcoded ones.
"""

import json
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
except ImportError:
    pass

import firebase_admin
from firebase_admin import messaging
from database._client import db

def get_real_token_from_database():
    """Get a real FCM token from database"""
    try:
        users_ref = db.collection('users')
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                return user_data['fcm_token'], doc.id
        return None, None
    except Exception as e:
        print(f"Error getting token: {e}")
        return None, None

def test_with_real_token():
    """Test notification with real database token"""
    # Initialize Firebase
    if not os.environ.get('SERVICE_ACCOUNT_JSON'):
        print("❌ SERVICE_ACCOUNT_JSON not found")
        return
    
    try:
        firebase_admin.get_app()
    except ValueError:
        service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
        credentials = firebase_admin.credentials.Certificate(service_account_info)
        firebase_admin.initialize_app(credentials)
    
    # Get real token
    token, uid = get_real_token_from_database()
    if not token:
        print("❌ No FCM token found in database")
        return
    
    print(f"🧪 Testing with real token from user: {uid[:8]}...")
    print(f"   Token: {token[:30]}...")
    
    # Test notification
    try:
        message = messaging.Message(
            notification=messaging.Notification(
                title="Real Token Test",
                body="Testing with real database token"
            ),
            token=token
        )
        
        response = messaging.send(message)
        print(f"✅ Success: {response}")
        
    except Exception as e:
        print(f"❌ Failed: {e}")

if __name__ == "__main__":
    test_with_real_token()
'''
    
    with open('debug_with_real_tokens.py', 'w') as f:
        f.write(updated_script)
    
    print("✅ 创建了 debug_with_real_tokens.py")
    print("   这个脚本使用数据库中的真实 token 进行测试")

def main():
    """Main function"""
    print("=" * 60)
    print("🔍 FCM TOKEN 分析器")
    print("=" * 60)
    print("分析 debug_service_account_detailed.py 中使用的 token")
    print("以及它与 Bundle ID 的关系")
    print("=" * 60)
    
    # Analyze hardcoded token
    analyze_hardcoded_token()
    
    # Get all tokens from database
    tokens_info = get_all_tokens_from_database()
    
    # Analyze token-Bundle ID relationship
    analyze_token_bundle_id_relationship()
    
    # Provide recommendations
    provide_token_update_recommendations()
    
    # Create updated debug script
    create_updated_debug_script()
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("debug_service_account_detailed.py 使用硬编码 token")
    print("建议使用数据库中的真实 token 进行测试")
    print("=" * 60)

if __name__ == "__main__":
    main()
